/**
 * Global JavaScript to Disable Safari Reading Mode
 * Include this file in all pages of your website
 */

(function() {
    'use strict';
    
    // Configuration
    const config = {
        debug: false,
        aggressiveMode: true,
        monitorChanges: true
    };
    
    // Utility function for logging
    const log = (message) => {
        if (config.debug) {
            console.log('[Reading Mode Prevention]', message);
        }
    };
    
    // Main prevention function
    const preventReadingMode = () => {
        log('Running reading mode prevention...');
        
        // Set document-level attributes
        const docAttrs = {
            'data-reader': 'false',
            'data-article': 'false',
            'data-readability-score': '0',
            'data-commercial': 'true',
            'data-interactive': 'true',
            'data-page-type': 'commercial-website'
        };
        
        Object.entries(docAttrs).forEach(([key, value]) => {
            document.documentElement.setAttribute(key, value);
            document.body?.setAttribute(key, value);
        });
        
        // Add noise elements to confuse Safari's parser
        if (config.aggressiveMode) {
            addNoiseElements();
        }
        
        // Mark all elements appropriately
        markElements();
        
        // Inject prevention CSS
        injectPreventionCSS();
        
        // Modify meta tags
        modifyMetaTags();
        
        log('Reading mode prevention applied');
    };
    
    // Add invisible noise elements
    const addNoiseElements = () => {
        const noiseContainer = document.createElement('div');
        noiseContainer.style.cssText = 'position:absolute;top:-9999px;left:-9999px;width:1px;height:1px;opacity:0;pointer-events:none;';
        noiseContainer.setAttribute('aria-hidden', 'true');
        
        // Add various interactive elements
        const elements = [
            '<button type="button">Commercial Button</button>',
            '<input type="text" value="commercial-input">',
            '<select><option>Commercial Option</option></select>',
            '<a href="#commercial">Commercial Link</a>',
            '<form><input type="submit" value="Commercial Form"></form>',
            '<nav><a href="#nav">Navigation</a></nav>',
            '<div data-commercial="true">Commercial Content</div>',
            '<span data-interactive="true">Interactive Span</span>'
        ];
        
        noiseContainer.innerHTML = elements.join('');
        
        // Add to document
        if (document.body) {
            document.body.appendChild(noiseContainer);
            
            // Remove after Safari has parsed the page
            setTimeout(() => {
                if (noiseContainer.parentNode) {
                    noiseContainer.parentNode.removeChild(noiseContainer);
                }
            }, 2000);
        }
    };
    
    // Mark all elements with appropriate attributes
    const markElements = () => {
        const allElements = document.querySelectorAll('*');
        
        allElements.forEach(element => {
            if (!element.tagName) return;
            
            const tagName = element.tagName.toUpperCase();
            
            // Mark all elements as non-article
            element.setAttribute('data-reader', 'false');
            element.setAttribute('data-article', 'false');
            
            // Special handling for content elements
            if (['MAIN', 'SECTION', 'ARTICLE', 'DIV', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(tagName)) {
                element.setAttribute('data-commercial', 'true');
                element.setAttribute('data-interactive', 'true');
                
                // Set appropriate roles
                if (tagName === 'MAIN') {
                    element.setAttribute('role', 'main');
                } else if (tagName === 'ARTICLE') {
                    element.setAttribute('role', 'presentation');
                    element.setAttribute('data-content-type', 'commercial');
                } else {
                    element.setAttribute('role', 'presentation');
                }
            }
            
            // Mark interactive elements
            if (['BUTTON', 'A', 'INPUT', 'SELECT', 'TEXTAREA', 'FORM', 'NAV'].includes(tagName)) {
                element.setAttribute('data-interactive', 'true');
                element.setAttribute('data-reader', 'false');
                element.setAttribute('data-commercial', 'true');
            }
            
            // Special handling for navigation
            if (['NAV', 'HEADER', 'FOOTER', 'ASIDE'].includes(tagName)) {
                element.setAttribute('data-navigation', 'true');
                element.setAttribute('data-commercial', 'true');
            }
        });
    };
    
    // Inject CSS to prevent reading mode
    const injectPreventionCSS = () => {
        const existingStyle = document.getElementById('reading-mode-prevention');
        if (existingStyle) return;
        
        const style = document.createElement('style');
        style.id = 'reading-mode-prevention';
        style.textContent = `
            html, body {
                -webkit-reader-mode: disabled !important;
                -webkit-text-size-adjust: 100% !important;
            }
            
            [data-commercial="true"] {
                -webkit-reader-mode: disabled !important;
            }
            
            [data-interactive="true"] {
                -webkit-touch-callout: default;
                pointer-events: auto !important;
            }
            
            body::before {
                content: 'commercial-website';
                position: absolute;
                top: -9999px;
                left: -9999px;
                opacity: 0;
                font-size: 0;
            }
        `;
        
        document.head.appendChild(style);
    };
    
    // Modify meta tags to signal commercial content
    const modifyMetaTags = () => {
        const metaTags = [
            { name: 'reader-view', content: 'disabled' },
            { name: 'safari-reader', content: 'disabled' },
            { name: 'readability-score', content: '0' },
            { name: 'article', content: 'false' },
            { name: 'document-type', content: 'commercial-website' },
            { name: 'content-type', content: 'business-website' },
            { name: 'page-type', content: 'homepage' },
            { name: 'site-type', content: 'commercial' }
        ];
        
        metaTags.forEach(({ name, content }) => {
            let meta = document.querySelector(`meta[name="${name}"]`);
            if (!meta) {
                meta = document.createElement('meta');
                meta.setAttribute('name', name);
                document.head.appendChild(meta);
            }
            meta.setAttribute('content', content);
        });
        
        // Modify viewport if it exists
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            const currentContent = viewport.getAttribute('content') || '';
            if (!currentContent.includes('interactive-widget')) {
                viewport.setAttribute('content', currentContent + ', interactive-widget=resizes-content');
            }
        }
    };
    
    // Monitor for changes and reapply prevention
    const monitorChanges = () => {
        if (!config.monitorChanges) return;
        
        const observer = new MutationObserver((mutations) => {
            let shouldReapply = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldReapply = true;
                }
            });
            
            if (shouldReapply) {
                setTimeout(preventReadingMode, 100);
            }
        });
        
        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: false
            });
        }
    };
    
    // Initialize prevention
    const init = () => {
        log('Initializing reading mode prevention...');
        
        // Run immediately
        preventReadingMode();
        
        // Run on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', preventReadingMode);
        }
        
        // Run on window load
        window.addEventListener('load', () => {
            setTimeout(preventReadingMode, 100);
            setTimeout(preventReadingMode, 500);
            setTimeout(preventReadingMode, 1000);
        });
        
        // Monitor for changes
        monitorChanges();
        
        // Periodic reapplication (aggressive mode)
        if (config.aggressiveMode) {
            setInterval(preventReadingMode, 5000);
        }
        
        log('Reading mode prevention initialized');
    };
    
    // Start initialization
    init();
    
    // Expose global function for manual triggering
    window.preventReadingMode = preventReadingMode;
    
})();
