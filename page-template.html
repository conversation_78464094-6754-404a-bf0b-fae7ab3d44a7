<!DOCTYPE html>
<html lang="en" data-reader="false" data-article="false" data-commercial="true">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, interactive-widget=resizes-content">
    <title>Your Page Title - X-ZoneServers</title>
    <meta name="description" content="Your page description">
    
    <!-- Disable Safari Reading Mode - Multiple Methods -->
    <meta name="reader-view" content="disabled">
    <meta name="safari-reader" content="disabled">
    <meta name="readability-score" content="0">
    <meta name="article" content="false">
    <meta name="document-type" content="website">
    <meta name="content-type" content="commercial-website">
    <meta name="page-type" content="commercial">
    <meta name="site-type" content="business">
    <meta name="content-category" content="commercial">
    <meta name="reading-mode" content="disabled">
    <meta name="safari-reading-mode" content="off">
    <meta name="webkit-reader-mode" content="disabled">
    <meta name="application-name" content="X-ZoneServers">
    <meta name="msapplication-TileImage" content="/images/icon-144x144.png">
    
    <!-- Apple specific -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="X-ZoneServers">
    
    <!-- Prevent reading mode CSS and JS -->
    <link rel="stylesheet" href="disable-reading-mode.css">
    <script src="disable-reading-mode.js"></script>
    
    <!-- Your other CSS files -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Basic styles to prevent reading mode -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-reader-mode: disabled !important;
            -webkit-text-size-adjust: 100% !important;
        }
        
        .commercial-content {
            -webkit-reader-mode: disabled !important;
        }
        
        .interactive-element {
            -webkit-reader-mode: disabled !important;
            -webkit-touch-callout: default;
            pointer-events: auto !important;
        }
    </style>
</head>
<body class="antialiased" 
      data-reader="false" 
      data-article="false" 
      data-commercial="true" 
      data-interactive="true" 
      data-readability-score="0" 
      itemscope 
      itemtype="https://schema.org/WebApplication" 
      role="application">

    <!-- Skip links for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header -->
    <header data-commercial="true" data-interactive="true">
        <nav class="interactive-element" data-navigation="true">
            <!-- Your navigation here -->
            <a href="index.html" class="interactive-element">Home</a>
            <a href="services.html" class="interactive-element">Services</a>
            <a href="pricing.html" class="interactive-element">Pricing</a>
            <a href="contact.html" class="interactive-element">Contact</a>
        </nav>
    </header>

    <!-- Main content -->
    <main id="main-content" 
          role="main" 
          aria-label="Main content area" 
          data-reader="false" 
          data-article="false" 
          data-commercial="true" 
          itemscope 
          itemtype="https://schema.org/WebApplication">
        
        <!-- Your page content here -->
        <section class="commercial-content" data-commercial="true" data-interactive="true">
            <h1>Your Page Heading</h1>
            
            <!-- Always include some interactive elements -->
            <div class="interactive-element">
                <button type="button" class="interactive-element">Get Started</button>
                <a href="#contact" class="interactive-element">Contact Us</a>
            </div>
            
            <!-- Your content -->
            <div class="commercial-content" data-content-type="commercial">
                <p>Your page content goes here...</p>
            </div>
            
            <!-- Include pricing or commercial elements -->
            <div class="pricing-section commercial-content" data-commercial="true">
                <div class="price-card interactive-element">
                    <h3>Service Name</h3>
                    <p class="price">€9.50/month</p>
                    <button type="button" class="interactive-element">Order Now</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer data-commercial="true" data-navigation="true">
        <nav class="interactive-element">
            <a href="terms.html" class="interactive-element">Terms</a>
            <a href="privacy.html" class="interactive-element">Privacy</a>
            <a href="support.html" class="interactive-element">Support</a>
        </nav>
        <p>&copy; 2025 X-ZoneServers. All rights reserved.</p>
    </footer>

    <!-- Your other JavaScript files -->
    <script defer src="https://cdn.tailwindcss.com"></script>
    
    <!-- Additional prevention script for this page -->
    <script>
        // Page-specific reading mode prevention
        document.addEventListener('DOMContentLoaded', function() {
            // Add more interactive elements dynamically
            const interactiveDiv = document.createElement('div');
            interactiveDiv.style.cssText = 'position:absolute;top:-9999px;opacity:0;';
            interactiveDiv.innerHTML = `
                <button>Hidden Button</button>
                <input type="text" value="hidden">
                <select><option>Hidden Option</option></select>
                <form><input type="submit"></form>
            `;
            document.body.appendChild(interactiveDiv);
            
            // Remove after Safari parses
            setTimeout(() => {
                if (interactiveDiv.parentNode) {
                    interactiveDiv.parentNode.removeChild(interactiveDiv);
                }
            }, 2000);
        });
    </script>
</body>
</html>
