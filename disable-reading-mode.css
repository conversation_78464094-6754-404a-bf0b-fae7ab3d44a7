/* 
 * Global CSS to Disable Safari Reading Mode
 * Include this file in all pages of your website
 */

/* Prevent Safari Reading Mode Detection */
html, body {
    -webkit-reader-mode: disabled !important;
    -webkit-text-size-adjust: 100% !important;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Mark all major content elements as non-article */
main, section, article, div, p, h1, h2, h3, h4, h5, h6 {
    -webkit-reader-mode: disabled !important;
    -webkit-text-size-adjust: 100% !important;
}

/* Ensure interactive elements are preserved */
button, a, input, select, textarea, form, nav {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
    -webkit-user-select: auto;
    user-select: auto;
    pointer-events: auto !important;
}

/* Add noise to confuse Safari's content parser */
body::before {
    content: '';
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
    background: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="transparent"/><text x="0" y="0" font-size="1">commercial website</text></svg>');
}

/* Add invisible interactive elements to signal this is not an article */
body::after {
    content: '';
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
    background: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="transparent"/><foreignObject width="1" height="1"><button xmlns="http://www.w3.org/1999/xhtml">btn</button></foreignObject></svg>');
}

/* Prevent text selection that might trigger reading mode */
.prevent-reading-mode {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-reader-mode: disabled !important;
}

/* Commercial content indicators */
.commercial-content {
    -webkit-reader-mode: disabled !important;
    position: relative;
}

.commercial-content::before {
    content: attr(data-commercial-type, 'product');
    position: absolute;
    top: -9999px;
    left: -9999px;
    opacity: 0;
    font-size: 0;
}

/* Interactive sections */
.interactive-section {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
    cursor: pointer;
}

/* Navigation elements */
nav, .navigation, .menu {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
}

/* Form elements */
form, .form-container {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
}

/* Pricing and commercial tables */
.pricing-table, .product-grid, .service-list {
    -webkit-reader-mode: disabled !important;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Footer elements */
footer {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
}

/* Header elements */
header {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
}

/* Sidebar elements */
aside, .sidebar {
    -webkit-reader-mode: disabled !important;
    -webkit-touch-callout: default;
}

/* Media queries for different devices */
@media screen and (max-width: 768px) {
    body {
        -webkit-text-size-adjust: 100% !important;
        -webkit-reader-mode: disabled !important;
    }
}

@media screen and (min-width: 769px) {
    body {
        -webkit-text-size-adjust: 100% !important;
        -webkit-reader-mode: disabled !important;
    }
}

/* Accessibility considerations while preventing reading mode */
@media (prefers-reduced-motion: reduce) {
    * {
        -webkit-reader-mode: disabled !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    * {
        -webkit-reader-mode: disabled !important;
    }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    * {
        -webkit-reader-mode: disabled !important;
    }
}

/* Print styles - also prevent reading mode in print preview */
@media print {
    * {
        -webkit-reader-mode: disabled !important;
    }
}
