/* X-ZoneServers WHMCS Custom Styles */

/* Base styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #020617 0%, #0f172a 50%, #020617 100%);
    min-height: 100vh;
    color: #e2e8f0;
}

/* Skip links for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
    border-radius: 4px;
}

.skip-link:focus {
    top: 6px;
}

/* Gradient utilities */
.bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Hero gradient background matching homepage */
.hero-gradient {
    background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
}

/* Login page specific styles */
.login-page {
    background: linear-gradient(135deg, #020617 0%, #0f172a 50%, #020617 100%);
    min-height: 100vh;
}

/* Gradient text utilities */
.gradient-text-primary {
    background: linear-gradient(to right, #60a5fa, #a855f7, #ec4899, #f97316);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Button styles matching homepage */
.btn-primary {
    background: linear-gradient(to right, #3b82f6, #8b5cf6, #ec4899);
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 700;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.25);
}

/* Form input styles */
.form-control-modern {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.75rem;
    color: white;
    transition: all 0.3s ease;
}

.form-control-modern:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Content area styling */
.content-area {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.3);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

/* Panel styling */
.panel {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.panel-heading {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    padding: 1rem 1.5rem;
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
}

/* Form styling */
.form-control {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 6px;
    color: #e2e8f0;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* Button styling */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: rgba(51, 65, 85, 0.8);
    color: #e2e8f0;
    border: 1px solid rgba(71, 85, 105, 0.5);
}

.btn-secondary:hover {
    background: rgba(71, 85, 105, 0.8);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

/* Table styling */
.table {
    width: 100%;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(51, 65, 85, 0.3);
}

.table th {
    background: rgba(51, 65, 85, 0.8);
    color: #f1f5f9;
    padding: 1rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(71, 85, 105, 0.5);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(51, 65, 85, 0.3);
    color: #cbd5e1;
}

.table tr:hover {
    background: rgba(51, 65, 85, 0.2);
}

/* Alert styling */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: #10b981;
    color: #6ee7b7;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
    color: #93c5fd;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
    color: #fbbf24;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
    color: #fca5a5;
}

/* Full-width content optimization */
.content-area {
    max-width: none;
    width: 100%;
}

/* Navigation breadcrumbs for better UX without sidebar */
.breadcrumb {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
}

.breadcrumb-item {
    color: #cbd5e1;
}

.breadcrumb-item.active {
    color: #3b82f6;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #64748b;
    margin: 0 0.5rem;
}

/* Progress bar styling */
.progress {
    background: rgba(51, 65, 85, 0.5);
    border-radius: 6px;
    height: 8px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    height: 100%;
    transition: width 0.3s ease;
}

/* Modal styling */
.modal-content {
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 12px;
    backdrop-filter: blur(20px);
}

.modal-header {
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    color: #f1f5f9;
}

.modal-body {
    color: #cbd5e1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content-area,
    .panel,
    .sidebar {
        padding: 1rem;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Login page hero badges */
.login-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.login-badge-secure {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.login-badge-instant {
    background: linear-gradient(to right, rgba(16, 185, 129, 0.2), rgba(6, 182, 212, 0.2));
    border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Login card styles */
.login-card {
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Security info cards */
.security-card {
    background: rgba(15, 23, 42, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.security-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Captcha container styling */
.captcha-container {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: rgba(30, 41, 59, 0.3);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80px;
}

/* reCAPTCHA widget styling */
.captcha-container iframe,
.captcha-container div,
.captcha-container .g-recaptcha,
.captcha-container .h-captcha {
    max-width: 100%;
    margin: 0 auto;
}

/* Default captcha image styling */
.captcha-container img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
}

/* Captcha input field styling */
.captcha-container input[type="text"] {
    width: 100%;
    padding: 0.5rem;
    margin-top: 0.5rem;
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 0.375rem;
    color: white;
    font-size: 0.875rem;
}

.captcha-container input[type="text"]:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(51, 65, 85, 0.3);
}

::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status indicators */
.status-active {
    color: #10b981;
}

.status-suspended {
    color: #f59e0b;
}

.status-terminated {
    color: #ef4444;
}

.status-pending {
    color: #6b7280;
}

/* Pricing Cards */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.pricing-card:hover {
    transform: translateY(-8px);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
}

.pricing-card.featured {
    border: 2px solid;
    border-image: linear-gradient(135deg, #3b82f6, #8b5cf6) 1;
    position: relative;
}

.pricing-card.featured::before {
    content: "Most Popular";
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 1rem;
    text-align: center;
}

.pricing-price {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    line-height: 1;
}

.pricing-period {
    color: #94a3b8;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.pricing-features li {
    padding: 0.75rem 0;
    color: #cbd5e1;
    border-bottom: 1px solid rgba(51, 65, 85, 0.3);
    position: relative;
    padding-left: 2rem;
}

.pricing-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.pricing-features li:last-child {
    border-bottom: none;
}

/* Feature Cards */
.feature-card {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.3);
    border-radius: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-4px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
}

/* Button sizes */
.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(15, 23, 42, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(51, 65, 85, 0.3);
}

/* Form group and label styling - Missing classes for cart template */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.form-label i {
    margin-right: 0.25rem;
    color: #94a3b8;
}

/* Additional utility classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.w-full {
    width: 100%;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

/* Cart specific styling fixes */
.sticky {
    position: sticky;
}

.top-24 {
    top: 6rem;
}

.rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.border-l-0 {
    border-left-width: 0;
}

/* Ensure proper background for all pages */
html, body {
    background: linear-gradient(135deg, #020617 0%, #0f172a 50%, #020617 100%) !important;
    min-height: 100vh;
}

/* Fix for potential Tailwind conflicts */
.container.mx-auto {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container.mx-auto {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container.mx-auto {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}
